import datetime
from datetime import timezone
import mt5_enums


class BridgeFunctions:
    def __init__(self, mt5):
        self.mt5 = mt5

    def get_account_info(self, data_type=None):
        """
        Retrieve detailed account information

        :return: Dictionary with account details
        """
        # Get account information
        account_info = self.mt5.account_info()
        if account_info is None:
            return {"success": False, "error": "Unable to retrieve account information"}

        return {
            "type": data_type,
            "success": True,
            "balance": account_info.balance,
            "equity": account_info.equity,
            "margin": account_info.margin,
            "free_margin": account_info.margin_free,
            "margin_level": account_info.margin_level,
            "profit": account_info.profit,
            "server": account_info.server,
            "trade_mode": account_info.trade_mode,
        }

    def get_open_positions(self, data_type=None):
        """
        Retrieve all open positions

        :return: List of dictionaries with position details
        """
        # Get all open positions
        positions = self.mt5.positions_get()

        if positions is None:
            return {
                "success": False,
                "error": "Unable to retrieve positions",
                "positions": [],
            }

        # Process positions into a more readable format
        processed_positions = []
        for position in positions:
            processed_positions.append(
                {
                    "ticket": position.ticket,
                    "symbol": position.symbol,
                    "type": position.type,
                    "volume": position.volume,
                    "open_price": position.price_open,
                    "current_price": position.price_current,
                    "stop_loss": position.sl,
                    "take_profit": position.tp,
                    "profit": position.profit,
                    "comment": position.comment,
                    "magic": position.magic,
                }
            )

        return {
            "type": data_type,
            "success": True,
            "positions": processed_positions,
            "total_positions": len(processed_positions),
        }

    def get_pending_orders(self, data_type=None):
        """
        Retrieve all pending orders

        :return: List of dictionaries with order details
        """
        # Get all pending orders
        orders = self.mt5.orders_get()

        if orders is None:
            return {
                "success": False,
                "error": "Unable to retrieve orders",
                "orders": [],
            }

        # Process orders into a more readable format
        processed_orders = []
        for order in orders:
            processed_orders.append(
                {
                    "ticket": order.ticket,
                    "symbol": order.symbol,
                    "type": order.type,
                    "volume": order.volume_current,
                    "price_open": order.price_open,
                    "price_current": order.price_current,
                    "stop_loss": order.sl,
                    "take_profit": order.tp,
                    "comment": order.comment,
                    "time_setup": order.time_setup,
                    "time_expiration": order.time_expiration,
                    "magic": order.magic,
                }
            )

        return {
            "type": data_type,
            "success": True,
            "orders": processed_orders,
            "total_orders": len(processed_orders),
        }

    def close_trade(
        self, ticket, volume=None, price=None, deviation=None, data_type=None
    ):
        """
        Close an existing open trade

        :param ticket: The ticket number of the trade to close
        :param volume: Volume to close (optional, defaults to full position)
        :return: Boolean indicating success or failure
        """
        # Retrieve the position details
        position = self.mt5.positions_get(ticket=ticket)
        orders = self.mt5.orders_get(ticket=ticket)

        if not position:
            print(f"No position found with ticket {ticket}")

        if not orders:
            print(f"No order found with ticket {ticket}")
        
        if not position and not orders:
            return {
                "type": data_type,
                "success": False,
                "error": f"Trade failed, error: Ticket {ticket} does not exist",
            }
            
        if orders:
            request = {
                "action": mt5_enums.TRADE_ACTION_REMOVE,
                "order": ticket,
                "symbol": orders[0].symbol,
                "magic": orders[0].magic,
                "comment": "Python script delete",
            }
            
            # Send the close trade request
            result = self.mt5.order_send(request)

            if result is None:
                return {
                    "type": data_type,
                    "success": False,
                    "error": f"Trade failed, error: {self.mt5.last_error()}",
                }

            if result.retcode != self.mt5.TRADE_RETCODE_DONE:
                return {
                    "type": data_type,
                    "success": False,
                    "error": f"Trade failed, error: {result.retcode}, comment: {result.comment}",
                    "data": result._asdict(),
                }

            print(f"Trade closed successfully, result: {result}")
            return {
                "type": data_type,
                "success": True,
                "error": None,
                "data": result._asdict(),
            }

        elif position:
            # If no volume specified, close the entire position
            if volume is None:
                volume = abs(position[0].volume)

            # Determine the closing order type (opposite of the original trade)
            close_type = (
                mt5_enums.ORDER_TYPE_SELL
                if position[0].type == mt5_enums.POSITION_TYPE_BUY
                else mt5_enums.ORDER_TYPE_BUY
            )

            # Get current market price
            symbol = position[0].symbol
            if not self.mt5.symbol_select(symbol, True):
                print(f"Failed to select symbol {symbol}")
                return {
                    "type": data_type,
                    "success": False,
                    "error": f"Trade failed, error: {self.mt5.last_error()}",
                }

            tick = self.mt5.symbol_info_tick(symbol)

            if price is None:
                price = tick.bid if close_type == self.mt5.ORDER_TYPE_SELL else tick.ask

            if deviation is None:
                deviation = 10

            # Prepare the close trade request
            request = {
                "action": self.mt5.TRADE_ACTION_DEAL,
                "symbol": symbol,
                "volume": volume,
                "type": close_type,
                "position": ticket,
                "price": price,
                "deviation": int(deviation),
                "comment": "Close trade",
            }

            # Send the close trade request
            result = self.mt5.order_send(request)

            if result is None:
                return {
                    "type": data_type,
                    "success": False,
                    "error": f"Trade failed, error: {self.mt5.last_error()}",
                }

            if result.retcode != self.mt5.TRADE_RETCODE_DONE:
                return {
                    "type": data_type,
                    "success": False,
                    "error": f"Trade failed, error: {result.retcode}, comment: {result.comment}",
                    "data": result._asdict(),
                }

            print(f"Trade closed successfully, result: {result}")
            return {
                "type": data_type,
                "success": True,
                "error": None,
                "data": result._asdict(),
            }

    def get_available_symbols(self, data_type=None):
        """
        Retrieve all available trading symbols for the current account

        :return: Dictionary with symbol details
        """
        # Get all symbols
        symbols = self.mt5.symbols_get()

        if symbols is None:
            return {
                "success": False,
                "error": "Unable to retrieve symbols",
                "symbols": [],
            }

        # Process symbols into a more readable format
        processed_symbols = []
        for symbol in symbols:
            processed_symbols.append(
                {
                    "name": symbol.name,
                    "description": symbol.description,
                    "path": symbol.path,
                    "digits": symbol.digits,
                    "spread": symbol.spread,
                    "time": symbol.time,
                }
            )

        return {
            "type": data_type,
            "success": True,
            "symbols": processed_symbols,
            "total_symbols": len(processed_symbols),
        }

    def get_trade_history(self, start_date=None, end_date=None, data_type=None):
        """
        Retrieve trade history for the account

        :param start_date: Start date for history retrieval (optional)
        :param end_date: End date for history retrieval (optional)
        :return: Dictionary with trade history details
        """
        # If no dates provided, retrieve last 30 days of history
        if start_date is None or end_date is None:
            from datetime import datetime, timedelta

            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

        # Retrieve deals (trades)
        deals = self.mt5.history_deals_get(start_date, end_date)

        if deals is None:
            return {
                "success": False,
                "error": "Unable to retrieve trade history",
                "deals": [],
            }

        # Process deals into a more readable format
        processed_deals = []
        for deal in deals:

            processed_deals.append(
                {
                    "ticket": deal.ticket,
                    "order": deal.order,
                    "time": deal.time,
                    "type": deal.type,
                    "entry": deal.entry,
                    "symbol": deal.symbol,
                    "volume": deal.volume,
                    "price": deal.price,
                    "commission": deal.commission,
                    "swap": deal.swap,
                    "profit": deal.profit,
                    "magic": deal.magic,
                    "comment": deal.comment,
                }
            )

        return {
            "type": data_type,
            "success": True,
            "deals": processed_deals,
            "total_deals": len(processed_deals),
        }

    def get_ticks_from(
        self, symbol="EURUSD", start_date=None, length=10000, data_type=None
    ):

        if start_date is None:
            from datetime import datetime, timedelta

            start_date = datetime.now() - timedelta(days=30)

        # Retrieve deals (trades)
        ticks = self.mt5.copy_ticks_from(
            symbol, start_date, int(length), self.mt5.COPY_TICKS_ALL
        )

        if ticks is None:
            return {
                "success": False,
                "error": "Unable to retrieve ticks",
                "deals": [],
            }

        # Process deals into a more readable format
        processed_ticks = []
        for tick in ticks:
            # print(tick)
            processed_ticks.append(
                {
                    "time": int(tick[0]),  # Convert to int for consistency
                    "bid": float(tick[1]),  # Convert to float
                    "ask": float(tick[2]),  # Convert to float
                }
            )

        return {
            "type": data_type,
            "success": True,
            "ticks": processed_ticks,
            "total_ticks": len(processed_ticks),
        }

    def get_ticks_range(
        self, symbol="EURUSD", start_date=None, end_date=None, data_type=None
    ):

        # Retrieve deals (trades)
        ticks = self.mt5.copy_ticks_range(
            symbol, start_date, end_date, self.mt5.COPY_TICKS_ALL
        )

        if ticks is None:
            return {
                "success": False,
                "error": "Unable to retrieve ticks",
                "deals": [],
            }

        processed_ticks = []
        for tick in ticks:
            # print(tick)
            processed_ticks.append(
                {
                    "time": int(tick[0]),  # Convert to int for consistency
                    "bid": float(tick[1]),  # Convert to float
                    "ask": float(tick[2]),  # Convert to float
                }
            )

        return {
            "type": data_type,
            "success": True,
            "ticks": processed_ticks,
            "total_ticks": len(processed_ticks),
        }

    def modify_trade(
        self,
        ticket,
        expiration=None,
        price=None,
        stop_loss=None,
        take_profit=None,
        data_type=None,
    ):
        """
        Modify an existing open trade's stop loss and/or take profit

        :param ticket: The ticket number of the trade to modify
        :param stop_loss: New stop loss price (optional)
        :param take_profit: New take profit price (optional)
        :return: Boolean indicating success or failure
        """
        # First, get the position details
        position = self.mt5.positions_get(ticket=ticket)

        if not position:
            print(f"No position found with ticket {ticket}")
            return position

        # Prepare the modification request
        request = {
            "action": self.mt5.TRADE_ACTION_SLTP,
            "position": ticket,
            "symbol": position[0].symbol,
            "magic": 234000,  # Same magic number as in place_trade
        }

        if price is not None:
            request["price"] = price

        if expiration is not None:
            request["expiration"] = expiration

        # Add stop loss if provided
        if stop_loss is not None:
            request["sl"] = stop_loss

        # Add take profit if provided
        if take_profit is not None:
            request["tp"] = take_profit

        # Send the modification request
        result = self.mt5.order_send(request)

        if result == None:
            return {
                "success": False,
                "error": f"Trade failed, error: {self.mt5.last_error()}",
            }

        # Send order
        result = self.mt5.order_send(request)
        if result.retcode == mt5_enums.TRADE_RETCODE_ERROR:
            print(f"Trade modification failed, error: {result.retcode}")
            return {
                "type": data_type,
                "success": False,
                "error": f"Trade failed, error: {result.retcode}, comment: {result.comment}",
            }

        print(f"Trade placed successfully, result: {result}")
        return {
            "type": data_type,
            "success": True,
            "error": None,
            "message": f"{result.comment}",
        }

    # Execute a simple trade operation
    def place_trade(
        self,
        symbol,
        action_type,
        volume,
        stop_loss=None,
        take_profit=None,
        comment="",
        data_type=None,
        price=None,
        order=None,
        stop_limit=None,
        deviation=10,
        expiration=None,
        position=None,
        position_by=None,
        magic=234000,
    ):
        if not self.mt5.symbol_select(symbol, True):
            print(f"Failed to select symbol {symbol}, error: {self.mt5.last_error()}")
            return {
                "type": data_type,
                "success": False,
                "error": f"Trade failed, error: {self.mt5.last_error()}",
            }

        # Get current price
        tick = self.mt5.symbol_info_tick(symbol)

        if price is None:
            price = (
                tick.ask
                if (
                    action_type == mt5_enums.ORDER_TYPE_BUY
                    or action_type == mt5_enums.ORDER_TYPE_BUY_LIMIT
                    or action_type == mt5_enums.ORDER_TYPE_BUY_STOP
                    or action_type == mt5_enums.ORDER_TYPE_BUY_STOP_LIMIT
                )
                else tick.bid
            )

        action = (
            self.mt5.TRADE_ACTION_DEAL
            if (
                action_type == mt5_enums.ORDER_TYPE_BUY
                or action_type == mt5_enums.ORDER_TYPE_SELL
            )
            else self.mt5.TRADE_ACTION_PENDING
        )

        # Prepare request dictionary
        request = {
            "action": action,
            "symbol": symbol,
            "volume": volume,
            "type": action_type,
            "price": float(price),
            "deviation": deviation,
            "magic": magic,  # Magic number to identify trades
            "comment": comment,
        }

        if order is not None:
            request["order"] = int(order)

        if stop_limit is not None:
            request["stoplimit"] = float(stop_limit)

        if expiration is not None and isinstance(expiration, int) and expiration > 0:
            # Convert seconds timestamp to timezone-aware datetime in UTC
            request["expiration"] = datetime.datetime.fromtimestamp(expiration, tz=timezone.utc)

        if position is not None:
            request["position"] = int(position)

        if position_by is not None:
            request["position_by"] = int(position_by)

        # Calculate stop loss and take profit prices if provided
        if stop_loss is not None or take_profit is not None:
            if stop_loss is not None:
                request["sl"] = stop_loss  # Stop loss price for buy order
            if take_profit is not None:
                request["tp"] = take_profit  # Take profit price for buy order

        # Send order
        result = self.mt5.order_send(request)

        if result == None:
            return {
                "type": data_type,
                "success": False,
                "error": f"Trade failed, error: {self.mt5.last_error()}",
            }

        if result.retcode != self.mt5.TRADE_RETCODE_DONE:
            print(f"Trade failed, error: {result.retcode}")
            return {
                "type": data_type,
                "success": False,
                "error": f"Trade failed, error: {result.retcode}, comment: {result.comment}",
                "data": result._asdict(),
            }

        print(f"Trade placed successfully, result: {result}")
        return {
            "type": data_type,
            "success": True,
            "error": None,
            "data": result._asdict(),
        }

    def manage_symbol(self, symbol, action, data_type=None):
        """
        Manage symbol selection and availability

        :param symbol: Name of the symbol to manage
        :param action: Action to perform ('select', 'unselect', 'enable', 'disable')
        :return: Dictionary with operation result
        """
        if not symbol:
            return {"success": False, "error": "Symbol name is required"}

        if action == "select":
            # Select the symbol for trading
            result = self.mt5.symbol_select(symbol, True)
            status_message = "Selected" if result else "Failed to select"

        elif action == "unselect":
            # Unselect the symbol
            result = self.mt5.symbol_select(symbol, False)
            status_message = "Unselected" if result else "Failed to unselect"

        else:
            return {
                "success": False,
                "error": f"Invalid action: {action}. Allowed actions are 'select', 'unselect', 'enable', 'disable'",
            }

        # Get current symbol info after action
        symbol_info = self.mt5.symbol_info(symbol)._asdict()

        return {
            "type": data_type,
            "success": result,
            "symbol": symbol,
            "action": action,
            "message": status_message,
            "details": symbol_info,
        }
