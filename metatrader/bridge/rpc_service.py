import asyncio
from typing import Any, Type
import grpc  # type: ignore
from concurrent import futures
import bridge_pb2_grpc as pb2_grpc
import bridge_pb2 as pb2
import mt5_functions
from mt4_bridge import Metatrader4 as mt4
from timestamp_utils import TimestampConverter


mt5 = None
mt5_fns: mt5_functions.BridgeFunctions = None
mt4_fns: mt4 = None
terminal_type = None


class Service(pb2_grpc.BridgeRpcServiceServicer):

    def __init__(self):
        self.ticking = False

    async def TickStart(self, request: Type[pb2.TickStartRequest], context):
        # if self.ticking:
        #     raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, "Already ticking")
        # self.ticking = True

        can_tick = True

        def on_rpc_done(_):
            print("Rpc done")
            nonlocal can_tick
            can_tick = False

        context.add_done_callback(on_rpc_done)

        while can_tick:
            tick = None
            if terminal_type == "mt5":
                tick = mt5.symbol_info_tick(request.symbol)
                if tick:
                    yield pb2.TickType(
                        ask=tick.ask,
                        bid=tick.bid,
                        time=TimestampConverter.seconds_to_milliseconds(tick.time),
                    )
            if terminal_type == "mt4":
                tick = await mt4_fns.get_symbol_tick(symbol=request.symbol)
                if tick:
                    yield pb2.TickType(
                        ask=tick["Ask"],
                        bid=tick["Bid"],
                        time=TimestampConverter.seconds_to_milliseconds(tick["MtTime"]),
                    )

            await asyncio.sleep(request.rate)

    def TickStop(self, request, context):
        self.ticking = False
        return pb2.EmptyType()

    async def GetAccount(self, request, context):
        if terminal_type == "mt5":
            res = mt5_fns.get_account_info()
            if res:
                return pb2.GetAccountResponse(
                    balance=res["balance"],
                    equity=res["equity"],
                    margin=res["margin"],
                    free_margin=res["free_margin"],
                    margin_level=res["margin_level"],
                    profit=res["profit"],
                    server=res["server"],
                    trade_mode=res["trade_mode"],
                )

        if terminal_type == "mt4":
            res = await mt4_fns.get_account_info()
            if res:
                return pb2.GetAccountResponse(
                    balance=res["Balance"],
                    equity=res["Equity"],
                    margin=res["Margin"],
                    free_margin=res["FreeMargin"],
                    margin_level=0,
                    profit=res["Profit"],
                    server=res["Server"],
                    trade_mode=res["MarginMode"],
                )

    async def GetPositions(self, request, context):

        if terminal_type == "mt5":
            res = mt5_fns.get_open_positions()
            if res.get("positions") is not None:
                return pb2.GetPositionsResponse(
                    positions=[
                        pb2.AccountPositionType(
                            ticket=position["ticket"],
                            symbol=position["symbol"],
                            type=position["type"],
                            volume=position["volume"],
                            open_price=position["open_price"],
                            current_price=position["current_price"],
                            stop_loss=position["stop_loss"],
                            take_profit=position["take_profit"],
                            comment=position["comment"],
                            magic=position["magic"],
                        )
                        for position in res["positions"] or []
                    ],
                    total_positions=res["total_positions"],
                )

        if terminal_type == "mt4":
            res = await mt4_fns.get_open_positions()
            if res is not None:
                return pb2.GetPositionsResponse(
                    positions=[
                        pb2.AccountPositionType(
                            ticket=position["Ticket"],
                            symbol=position["Symbol"],
                            type=position["Type"],
                            volume=position["Lots"],
                            open_price=position["OpenPrice"],
                            current_price=position["ClosePrice"],
                            stop_loss=position["StopLoss"],
                            take_profit=position["TakeProfit"],
                            comment=position["Comment"],
                            magic=position["MagicNumber"],
                        )
                        for position in res or []
                    ],
                    total_positions=len(res),
                )

    async def GetOrders(self, request, context):

        if terminal_type == "mt5":
            res = mt5_fns.get_pending_orders()
            if res.get("orders") is not None:
                return pb2.GetOrdersResponse(
                    orders=[
                        pb2.AccountOrdersType(
                            ticket=order["ticket"],
                            symbol=order["symbol"],
                            type=order["type"],
                            volume=order["volume"],
                            price_open=order["price_open"],
                            price_current=order["price_current"],
                            stop_loss=order["stop_loss"],
                            take_profit=order["take_profit"],
                            comment=order["comment"],
                            time_setup=TimestampConverter.seconds_to_milliseconds(
                                order["time_setup"]
                            ),
                            time_expiration=TimestampConverter.seconds_to_milliseconds(
                                order["time_expiration"]
                            ),
                            magic=order["magic"],
                        )
                        for order in res["orders"] or []
                    ],
                    total_orders=res["total_orders"],
                )
            else:
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, "Unable to retrieve orders"
                )

        if terminal_type == "mt4":
            res = await mt4_fns.get_pending_orders()
            if res is not None:
                return pb2.GetOrdersResponse(
                    orders=[
                        pb2.AccountOrdersType(
                            ticket=position["Ticket"],
                            symbol=position["Symbol"],
                            type=position["Type"],
                            volume=position["Lots"],
                            price_open=position["OpenPrice"],
                            price_current=position["ClosePrice"],
                            stop_loss=position["StopLoss"],
                            take_profit=position["TakeProfit"],
                            comment=position["Comment"],
                            time_expiration=TimestampConverter.seconds_to_milliseconds(
                                position["Expiration"]
                            ),
                            time_setup=0,
                            magic=position["MagicNumber"],
                        )
                        for position in res or []
                    ],
                    total_orders=len(res),
                )
            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def CloseTrade(self, request: Type[pb2.CloseTradeRequest], context):
        print(request)
        if terminal_type == "mt5":
            res = mt5_fns.close_trade(
                ticket=request.ticket,
                volume=request.volume,
                price=request.price,
                deviation=int(request.slippage),
            )
            if res["success"] == True:
                return pb2.GenericResponseType(
                    status=1,
                    message="Trade closed",
                )
            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            res: int = await mt4_fns.close_trade(
                ticket=request.ticket,
                lots=request.volume,
                price=request.price,
                slippage=request.slippage,
            )
            if res == 1:
                return pb2.GenericResponseType(
                    status=1,
                    message="Trade closed",
                )
            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def GetAvailableSymbols(self, request, context):
        if terminal_type == "mt5":
            res = mt5_fns.get_available_symbols()

            if res.get("symbols") is not None:
                return pb2.GetAvailableSymbolsResponse(
                    symbols=[
                        pb2.SymbolsType(
                            name=symbol["name"],
                            description=symbol["description"],
                            path=symbol["path"],
                            digits=symbol["digits"],
                            spread=symbol["spread"],
                            time=TimestampConverter.seconds_to_milliseconds(
                                symbol["time"]
                            ),
                        )
                        for symbol in res["symbols"] or []
                    ],
                    total_symbols=res["total_symbols"],
                )
            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            res = await mt4_fns.get_available_symbols()
            if res is not None:
                return pb2.GetAvailableSymbolsResponse(
                    symbols=[
                        pb2.SymbolsType(
                            name=symbol["Name"],
                            description=symbol["Description"],
                            path=symbol["Path"],
                            digits=int(symbol["Digits"]),
                            spread=float(symbol["Spread"]),
                            time=TimestampConverter.seconds_to_milliseconds(
                                int(symbol["Time"])
                            ),
                        )
                        for symbol in res or []
                    ],
                    total_symbols=len(res),
                )
            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def GetTradeHistory(self, request: Type[pb2.GetTradeHistoryRequest], context):
        try:
            # Convert milliseconds timestamps to UTC datetime objects
            start, end = TimestampConverter.validate_timestamp_range(
                request.start_date, request.end_date, is_milliseconds=True
            )
        except ValueError as e:
            raise grpc.RpcError(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"Invalid timestamp range: {str(e)}",
            )

        if terminal_type == "mt5":
            res = mt5_fns.get_trade_history(start_date=start, end_date=end)

            if res.get("success") == True:
                return pb2.GetTradeHistoryResponse(
                    deals=[
                        pb2.TradeDealType(
                            ticket=deal["ticket"],
                            order=deal["order"],
                            time=TimestampConverter.seconds_to_milliseconds(
                                deal["time"]
                            ),
                            type=deal["type"],
                            entry=deal["entry"],
                            symbol=deal["symbol"],
                            volume=deal["volume"],
                            price=deal["price"],
                            commission=deal["commission"],
                            swap=deal["swap"],
                            profit=deal["profit"],
                            magic=deal["magic"],
                            comment=deal["comment"],
                        )
                        for deal in res["deals"] or []
                    ],
                    total_deals=res["total_deals"],
                )
            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            # Convert UTC datetime to seconds timestamp for MT4
            start_timestamp = TimestampConverter.datetime_to_timestamp(
                start, as_milliseconds=False
            )
            end_timestamp = TimestampConverter.datetime_to_timestamp(
                end, as_milliseconds=False
            )

            res = await mt4_fns.get_trade_history(
                start_date=start_timestamp, end_date=end_timestamp
            )

            if res is not None:
                return pb2.GetTradeHistoryResponse(
                    deals=[
                        pb2.TradeDealType(
                            ticket=deal["Ticket"],
                            order=0,
                            time=TimestampConverter.seconds_to_milliseconds(
                                deal["OpenTime"]
                            ),
                            type=deal["Type"],
                            entry=deal["OpenPrice"],
                            symbol=deal["Symbol"],
                            volume=deal["Lots"],
                            price=deal["ClosePrice"],
                            commission=deal["Commission"],
                            swap=deal["Swap"],
                            profit=deal["Profit"],
                            magic=deal["MagicNumber"],
                            comment=deal["Comment"],
                        )
                        for deal in res or []
                    ],
                    total_deals=len(res),
                )
            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def GetTicksFrom(self, request: Type[pb2.GetTicksFromRequest], context):
        try:
            # Convert milliseconds timestamp to UTC datetime
            start = TimestampConverter.timestamp_to_utc_datetime(
                request.start_date, is_milliseconds=True
            )
        except ValueError as e:
            raise grpc.RpcError(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"Invalid start_date timestamp: {str(e)}",
            )

        if terminal_type == "mt5":

            res = mt5_fns.get_ticks_from(
                length=request.length, start_date=start, symbol=request.symbol
            )

            if res.get("ticks"):
                return pb2.GetTicksResponse(
                    ticks=[
                        pb2.TickType(
                            time=TimestampConverter.seconds_to_milliseconds(
                                tick["time"]
                            ),
                            bid=tick["bid"],
                            ask=tick["ask"],
                        )
                        for tick in res["ticks"] or []
                    ],
                    total_ticks=res["total_ticks"],
                )
            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            # Convert UTC datetime to seconds timestamp for MT4
            start_timestamp = TimestampConverter.datetime_to_timestamp(
                start, as_milliseconds=False
            )

            res = await mt4_fns.get_ticks_from(
                symbol=request.symbol,
                start_date=start_timestamp,
                length=request.length,
            )

            if res is not None:
                return pb2.GetTicksResponse(
                    ticks=[
                        pb2.TickType(
                            time=TimestampConverter.seconds_to_milliseconds(
                                tick["MtTime"]
                            ),
                            bid=tick["Close"],
                            ask=tick["Close"],
                        )
                        for tick in res or []
                    ],
                    total_ticks=len(res),
                )
            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def GetTicksRange(self, request: Type[pb2.GetTicksRangeRequest], context):
        try:
            # Convert milliseconds timestamps to UTC datetime objects
            start, end = TimestampConverter.validate_timestamp_range(
                request.start_date, request.end_date, is_milliseconds=True
            )
        except ValueError as e:
            raise grpc.RpcError(
                grpc.StatusCode.INVALID_ARGUMENT,
                f"Invalid timestamp range: {str(e)}",
            )

        if terminal_type == "mt5":
            res = mt5_fns.get_ticks_range(
                symbol=request.symbol, start_date=start, end_date=end
            )

            if res.get("ticks"):
                return pb2.GetTicksResponse(
                    ticks=[
                        pb2.TickType(
                            time=TimestampConverter.seconds_to_milliseconds(
                                tick["time"]
                            ),
                            bid=tick["bid"],
                            ask=tick["ask"],
                        )
                        for tick in res["ticks"] or []
                    ],
                    total_ticks=res["total_ticks"],
                )
            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            # Convert UTC datetime to seconds timestamps for MT4
            start_timestamp = TimestampConverter.datetime_to_timestamp(
                start, as_milliseconds=False
            )
            end_timestamp = TimestampConverter.datetime_to_timestamp(
                end, as_milliseconds=False
            )

            res = await mt4_fns.get_ticks_range(
                symbol=request.symbol,
                start_date=start_timestamp,
                end_date=end_timestamp,
            )

            if res is not None:
                return pb2.GetTicksResponse(
                    ticks=[
                        pb2.TickType(
                            time=TimestampConverter.seconds_to_milliseconds(
                                tick["MtTime"]
                            ),
                            bid=tick["Close"],
                            ask=tick["Close"],
                        )
                        for tick in res or []
                    ],
                    total_ticks=len(res),
                )
            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def ModifyTrade(self, request: Type[pb2.ModifyTradeRequest], context):

        if terminal_type == "mt5":
            res = mt5_fns.modify_trade(
                ticket=request.ticket,
                stop_loss=request.stop_loss,
                take_profit=request.take_profit,
                expiration=request.expiration,
                price=request.price,
            )

            if res["success"] == True:
                return pb2.GenericResponseType(
                    status=1,
                    message=f"Trade modified",
                )

            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            res = await mt4_fns.modify_trade(
                ticket=request.ticket,
                stop_loss=request.stop_loss,
                take_profit=request.take_profit,
                expiration=request.expiration,
                price=request.price,
            )
            if res == 1:
                return pb2.GenericResponseType(
                    status=1,
                    message=f"Trade modified",
                )

            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def PlaceTrade(self, request: Type[pb2.PlaceTradeRequest], context):
        # Convert expiration timestamp if provided (from milliseconds to seconds)
        expiration_timestamp = None
        if request.expiration and request.expiration > 0:
            try:
                expiration_timestamp = TimestampConverter.milliseconds_to_seconds(
                    request.expiration
                )
            except ValueError as e:
                raise grpc.RpcError(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    f"Invalid expiration timestamp: {str(e)}",
                )

        if terminal_type == "mt5":
            res = mt5_fns.place_trade(
                symbol=request.symbol,
                action_type=request.action_type,
                volume=request.volume,
                stop_loss=request.stop_loss,
                take_profit=request.take_profit,
                comment=request.comment,
                deviation=request.deviation,
                expiration=expiration_timestamp,
                magic=request.magic,
                order=request.order,
                position=request.position,
                position_by=request.position_by,
                price=request.price,
                stop_limit=request.stop_limit,
            )

            if res["success"] == True:
                data: dict[str, Any] = res["data"]
                return pb2.PlaceTradeResponse(
                    status=1,
                    message="Trade placed",
                    ticket=f"{data['ticket']}",
                )

            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

        if terminal_type == "mt4":
            res = await mt4_fns.place_trade(
                take_profit=request.take_profit,
                action=request.action_type,
                comment=request.comment,
                expiration=expiration_timestamp,
                magic=request.magic,
                price=request.price,
                stop_loss=request.stop_loss,
                symbol=request.symbol,
                slippage=request.deviation,
                volume=request.volume,
            )

            if res is not None:

                return pb2.PlaceTradeResponse(
                    status=1,
                    message="Trade placed",
                    ticket=f"{res}",
                )

            else:
                error = await mt4_fns.get_last_error()
                raise grpc.RpcError(
                    grpc.StatusCode.FAILED_PRECONDITION, f"Error ${error}"
                )

    async def ManageSymbol(self, request: Type[pb2.ManageSymbolRequest], context):
        if terminal_type == "mt5":
            res = mt5_fns.manage_symbol(
                action=request.action,
                symbol=request.symbol,
            )

            if res:
                symbol = res["details"]
                return pb2.ManageSymbolResponse(
                    status=1,
                    message=res["message"],
                    symbol=pb2.SymbolsType(
                        name=symbol["name"],
                        description=symbol["description"],
                        path=symbol["path"],
                        digits=symbol["digits"],
                        spread=symbol["spread"],
                        time=TimestampConverter.seconds_to_milliseconds(symbol["time"]),
                    ),
                )

            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])
        if terminal_type == "mt4":
            res = await mt4_fns.manage_symbol(
                action=request.action,
                symbol=request.symbol,
            )
            if res:
                return pb2.ManageSymbolResponse(
                    status=1,
                    message="Successfully set symbol",
                    symbol=pb2.SymbolsType(
                        name=res["Name"],
                        description=res["Description"],
                        path=res["Path"],
                        digits=int(res["Digits"]),
                        spread=float(res["Spread"]),
                        time=TimestampConverter.seconds_to_milliseconds(
                            int(res["Time"])
                        ),
                    ),
                )

            else:
                raise grpc.RpcError(grpc.StatusCode.FAILED_PRECONDITION, res["error"])

    async def GetTerminalError(self, request, context):
        if terminal_type == "mt5":
            return pb2.GenericResponseType(
                status=1,
                message=f"{mt5.last_error()}",
            )

        if terminal_type == "mt4":
            error = await mt4_fns.get_last_error()
            return pb2.GenericResponseType(
                status=1,
                message=f"{error}",
            )

    async def GetSymbolTick(self, request, context):
        if terminal_type == "mt5":
            tick = mt5.symbol_info_tick(request.symbol)
            if tick:
                return pb2.TickType(
                    ask=tick.ask,
                    bid=tick.bid,
                    time=TimestampConverter.seconds_to_milliseconds(tick.time),
                )
        if terminal_type == "mt4":
            tick = await mt4_fns.get_symbol_tick(symbol=request.symbol)
            if tick:
                return pb2.TickType(
                    ask=tick["Ask"],
                    bid=tick["Bid"],
                    time=TimestampConverter.seconds_to_milliseconds(tick["MtTime"]),
                )


class AuthInterceptor(grpc.aio.ServerInterceptor):
    def __init__(self, valid_token):
        self.valid_token = valid_token

    def intercept_service(self, continuation, handler_call_details):
        # Extract metadata (token)
        metadata = dict(handler_call_details.invocation_metadata)
        token = metadata.get("authorization")

        if token == self.valid_token:
            # Token is valid; proceed to the RPC handler
            return continuation(handler_call_details)
        else:
            # Invalid token; raise an error
            context = grpc.ServicerContext()
            context.abort(grpc.StatusCode.UNAUTHENTICATED, "Invalid token")


async def serve(port, auth_token, type: str, mt5Instance=None, mt4Instance: mt4 = None):
    global mt5, mt5_fns, mt4_fns, terminal_type

    terminal_type = type

    if terminal_type == "mt5":
        mt5 = mt5Instance
        mt5_fns = mt5_functions.BridgeFunctions(mt5Instance)
    elif terminal_type == "mt4":
        mt4_fns = mt4Instance
    else:
        raise ValueError(f"Unknown terminal type: {terminal_type}")

    server = grpc.aio.server(
        futures.ThreadPoolExecutor(max_workers=10),
        interceptors=[AuthInterceptor(valid_token=auth_token)],
    )
    pb2_grpc.add_BridgeRpcServiceServicer_to_server(Service(), server)

    server.add_insecure_port(f"[::]:{port}")
    await server.start()

    return server
